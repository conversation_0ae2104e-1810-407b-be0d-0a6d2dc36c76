\# Recoater HMI - User Guide



This guide explains how to use the Custom HMI to operate the Aerosint SPD Recoater.



\## 1. Starting the System



1\.  Ensure the Aerosint Recoater hardware is powered on and connected to the network.

2\.  Ensure the HMI software is running (see README.md for instructions).

3\.  Open the Firefox web browser on a computer connected to the same network.

4\.  Navigate to the HMI address (e.g., `http://\[IP\_OF\_HMI\_COMPUTER]:5173`).



\## 2. The Main Interface



The interface is divided into several main tabs, accessible from a navigation menu on the left:



\-   \*\*Status:\*\* The main landing page showing the overall system connection status.

\-   \*\*Recoater:\*\* Controls for the individual drums, hoppers, and the leveler.

\-   \*\*Axis:\*\* Manual controls for the X and Z axes.

\-   \*\*Print:\*\* For loading print files and managing the printing process.

\-   \*\*Configuration:\*\* For setting system-level parameters like the build area.



\### 2.1. Checking the Status



Upon loading the page, look at the top-right corner. A \*\*green "Connected"\*\* indicator means the HMI is successfully communicating with the recoater. If it is \*\*red "Disconnected"\*\*, please check network connections and ensure the recoater is on.



\### 2.2. Using the "Axis" Window



This window allows you to manually move the machine's axes.



1\.  \*\*Homing:\*\* Before any operation, it is recommended to \*\*Home\*\* each axis by pressing the "Home" button (`🏠`).

2\.  \*\*Manual Movement:\*\*

&nbsp;   -   Enter a `Distance (mm)` and `Speed (mm/s)`.

&nbsp;   -   Use the arrow buttons (`←`, `→`, `↑`, `↓`) to move the axes by the specified distance.

3\.  \*\*Gripper:\*\* Use the toggle switch to activate or deactivate the punch gripper.



\### 2.3. Using the "Recoater" Window



This window is for preparing the recoater drums.



1\.  \*\*Select a Component:\*\* Click on the graphical representation of a drum, hopper, or leveler to show its specific controls on the right.

2\.  \*\*Drum Control:\*\*

&nbsp;   -   Set the desired `Suction` and `Ejection` pressures.

&nbsp;   -   To manually rotate a drum, set a `Speed` and a number of `Turns`, then press the "Rotate" button (`🔄`).

3\.  \*\*Hopper Control (Scraping Blade):\*\*

&nbsp;   -   Select which screw to move (`Front`, `Back`, or `Both`).

&nbsp;   -   Enter a `Distance (µm)` and use the up/down arrows to move the blade.

&nbsp;   -   \*\*Always Home the blade (`🏠`) on startup.\*\*



\### 2.4. Running a Print Job ("Print" Window)



This is the main window for executing a print.



1\.  \*\*Load Geometry:\*\* For each drum you intend to use, click the "Upload" button (`⬆️`) and select the appropriate `.CLI` or `.PNG` file for that material.

2\.  \*\*Set Parameters:\*\* On the right-hand panel, configure all print parameters, such as `Patterning speed`, `Filling drum`, and `Layer thickness`.

3\.  \*\*Preview:\*\* The central area will show a preview of the deposited powder based on your loaded files.

4\.  \*\*Start Printing:\*\* Once everything is configured, press the \*\*"Start Printing"\*\* button.

5\.  \*\*Stop Printing:\*\* To cancel the active job, press the \*\*"Stop Printing"\*\* button.

