\# Recoater HMI - Developer Guide



This document provides technical details for developers working on the Recoater HMI. It includes an overview of the architecture and a detailed API reference.



\## 1. Architecture



The system uses a decoupled backend/frontend architecture.



\-   \*\*`backend` (FastAPI):\*\* Acts as a proxy and business logic layer. It communicates directly with the recoater hardware's API. It exposes a simplified REST API and a WebSocket to the frontend. This isolates the frontend from the complexities of the hardware API and allows for state management and background tasks (like polling).

\-   \*\*`frontend` (Vue.js):\*\* A pure Single-Page Application (SPA) that handles all user interaction. It gets all its data from the backend via API calls and receives real-time updates through a WebSocket connection. It never communicates with the recoater directly.



\## 2. Key Components



\-   \*\*`backend/services/recoater\\\_client.py`:\*\* A crucial Python class that implements methods for every call to the recoater's hardware API. All hardware communication should be placed here.

\-   \*\*`backend/app/api/recoater.py`:\*\* FastAPI router defining the REST endpoints that the frontend will consume.

\-   \*\*`backend/app/main.py`:\*\* Contains the main FastAPI application instance and the WebSocket logic for real-time updates.

\-   \*\*`frontend/src/views/`:\*\* Contains the main pages of the application, corresponding to the tabs in the UI.

\-   \*\*`frontend/src/components/`:\*\* Contains reusable UI elements (e.g., a single drum's control panel).



\## 3. Recoater Hardware API Reference



This reference is auto-generated from the provided `openapi.json` file. The backend service (`recoater\\\_client.py`) should implement methods to call these endpoints.



\### Recoater Endpoints



\#### `GET /config`

\- \*\*Summary:\*\* Get the recoater configuration variables.

\- \*\*Description:\*\* Returns the configuration variables of the recoater.



\#### `PUT /config`

\- \*\*Summary:\*\* Set the recoater configuration variables.

\- \*\*Description:\*\* Defines the configuration variables of the recoater.

\- \*\*Request Body (`application/json`):\*\*

  - `build\\\_space\\\_diameter` (number, optional): The diameter of the build space \[mm].

  - `build\\\_space\\\_dimensions` (object, optional):

    - `length` (number): The length of the build space \[mm].

    - `width` (number): The width of the build space \[mm].

  - `ejection\\\_matrix\\\_size` (integer, optional): The number of points in the ejection matrix.

  - `gaps` (array, optional): The list of gaps between the drums.

  - `resolution` (integer, optional): The resolution of the recoater, the size of one pixel \[µm].



\#### `GET /drums`

\- \*\*Summary:\*\* Get drums info.

\- \*\*Description:\*\* Returns information about the drums.



\#### `GET /drums/{drum\\\_id}`

\- \*\*Summary:\*\* Get drum info.

\- \*\*Description:\*\* Returns information about the specified drum.

\- \*\*Parameters:\*\*

  - `drum\\\_id` (integer, path, required): The drum's ID.



\#### `PUT /drums/{drum\\\_id}/config`

\- \*\*Summary:\*\* Set drum config.

\- \*\*Description:\*\* Defines the configuration of the specified drum.

\- \*\*Parameters:\*\*

  - `drum\\\_id` (integer, path, required): The drum's ID.

\- \*\*Request Body (`application/json`):\*\*

  - `powder\\\_offset` (integer, optional): The drum's powder offset \[pixel].

  - `theta\\\_offset` (number, required): The drum's theta offset \[mm].



\#### `PUT /drums/{drum\\\_id}/ejection`

\- \*\*Summary:\*\* Set drum ejection pressure.

\- \*\*Description:\*\* Defines the target ejection pressure that the specified drum has to reach.

\- \*\*Parameters:\*\*

  - `drum\\\_id` (integer, path, required): The drum's ID.

\- \*\*Request Body (`application/json`):\*\*

  - `target` (number, required): The target ejection pressure of the drum.

  - `unit` (string, optional): Units: pascal, bar.



\#### `PUT /drums/{drum\\\_id}/geometry`

\- \*\*Summary:\*\* Set the drum geometry.

\- \*\*Description:\*\* Defines the geometry of the specified drum. The geometry can either be a PNG file or a CLI file.

\- \*\*Parameters:\*\*

  - `drum\\\_id` (integer, path, required): The drum's ID.

\- \*\*Request Body (`application/octet-stream`):\*\* Binary data.



\#### `DELETE /drums/{drum\\\_id}/geometry`

\- \*\*Summary:\*\* Delete the drum geometry.

\- \*\*Description:\*\* Removes the current geometry of the specified drum.

\- \*\*Parameters:\*\*

  - `drum\\\_id` (integer, path, required): The drum's ID.



\#### `POST /drums/{drum\\\_id}/motion`

\- \*\*Summary:\*\* Post a motion command.

\- \*\*Description:\*\* Creates a motion command if possible.

\- \*\*Parameters:\*\*

  - `drum\\\_id` (integer, path, required): The drum's ID.

\- \*\*Request Body (`application/json`):\*\*

  - `mode` (string, required): Motion's mode: absolute, relative, turns, speed, homing.

  - `speed` (number, required): The speed of the motion \[mm/s].

  - `distance` (number, optional): The absolute or relative distance of the motion \[mm].

  - `turns` (number, optional): The number of turns of the motion.



\#### `DELETE /drums/{drum\\\_id}/motion`

\- \*\*Summary:\*\* Delete the current motion command.

\- \*\*Description:\*\* Cancels and removes the current motion command.

\- \*\*Parameters:\*\*

  - `drum\\\_id` (integer, path, required): The drum's ID.



\#### `PUT /drums/{drum\\\_id}/suction`

\- \*\*Summary:\*\* Set drum suction pressure target.

\- \*\*Description:\*\* Defines the target suction pressure that the specified drum has to reach.

\- \*\*Parameters:\*\*

  - `drum\\\_id` (integer, path, required): The drum's ID.

\- \*\*Request Body (`application/json`):\*\*

  - `target` (number, required): The target suction pressure of the drum \[Pa].



\#### `POST /drums/{drum\\\_id}/blade/screws/motion`

\- \*\*Summary:\*\* Post a motion command.

\- \*\*Description:\*\* Creates a motion command if possible.

\- \*\*Parameters:\*\*

  - `drum\\\_id` (integer, path, required): The drum's ID.

\- \*\*Request Body (`application/json`):\*\*

  - `mode` (string, required): Motion's mode: absolute, relative, homing.

  - `distance` (number, optional): The absolute or relative distance of the motion \[µm].



\#### `POST /drums/{drum\\\_id}/blade/screws/{screw\\\_id}/motion`

\- \*\*Summary:\*\* Post a motion command.

\- \*\*Description:\*\* Creates a motion command if possible for a single screw.

\- \*\*Parameters:\*\*

  - `drum\\\_id` (integer, path, required): The drum's ID.

  - `screw\\\_id` (integer, path, required): The drum blade screw's ID.

\- \*\*Request Body (`application/json`):\*\*

  - `distance` (number, required): The relative distance of the motion \[µm].



\#### `GET /layer/preview`

\- \*\*Summary:\*\* Get layer preview.

\- \*\*Description:\*\* Returns a PNG image preview of the layer.



\#### `PUT /layer/parameters`

\- \*\*Summary:\*\* Set layer's parameters.

\- \*\*Description:\*\* Defines the parameters of the current layer.

\- \*\*Request Body (`application/json`):\*\*

  - `filling\\\_id` (integer, required): The ID of the drum with the filling material powder.

  - `speed` (number, required): The patterning speed \[mm/s].

  - `powder\\\_saving` (boolean, optional, default: true): A flag indicating if the powder saving strategies are used.

  - `x\\\_offset` (number, optional): The offset along the X axis \[mm].



\#### `PUT /leveler/pressure`

\- \*\*Summary:\*\* Set leveler pressure target.

\- \*\*Description:\*\* Defines the target pressure that the leveler has to reach.

\- \*\*Request Body (`application/json`):\*\*

  - `target` (number, required): The target pressure of the leveler \[Pa].



\#### `POST /print/job`

\- \*\*Summary:\*\* Post a print job.

\- \*\*Description:\*\* Creates a printing job if the server is ready to start.



\#### `DELETE /print/job`

\- \*\*Summary:\*\* Delete the current print job.

\- \*\*Description:\*\* Cancels and removes the current printing job.



\#### `GET /state`

\- \*\*Summary:\*\* Get the recoater's state.

\- \*\*Description:\*\* Returns the current state of the recoater (ready, printing, error).



---

\*This is a summarized list. For full details on all endpoints and their responses, refer to the `openapi.json` file.\*

